require('dotenv').config({ path: '../../.env' });

console.log('🔍 Debugging API Startup Issues');
console.log('================================');

// Check environment variables
console.log('\n📋 Environment Check:');
console.log('NODE_ENV:', process.env.NODE_ENV || 'undefined');
console.log('DATABASE_URL:', process.env.DATABASE_URL ? '✅ Set' : '❌ Not set');
console.log('JWT_SECRET:', process.env.JWT_SECRET ? '✅ Set' : '❌ Not set');
console.log('API_PORT:', process.env.API_PORT || 'undefined');

// Test basic Node.js modules
console.log('\n📦 Module Loading Test:');
try {
  console.log('✅ dotenv loaded');
  
  const { NestFactory } = require('@nestjs/core');
  console.log('✅ @nestjs/core loaded');
  
  const { AppModule } = require('./dist/app.module');
  console.log('✅ AppModule loaded');
  
} catch (error) {
  console.error('❌ Module loading failed:', error.message);
  process.exit(1);
}

// Test database connection
console.log('\n🗄️  Database Connection Test:');
async function testDatabase() {
  try {
    const { prisma } = require('@revalu/database');
    console.log('✅ Database package loaded');
    
    await prisma.$connect();
    console.log('✅ Database connected');
    
    const result = await prisma.$queryRaw`SELECT 1 as test`;
    console.log('✅ Database query successful:', result);
    
    await prisma.$disconnect();
    console.log('✅ Database disconnected');
    
  } catch (error) {
    console.error('❌ Database test failed:', error.message);
    console.log('⚠️  Will continue without database (mock mode)');
  }
}

// Test NestJS application creation
console.log('\n🚀 NestJS Application Test:');
async function testNestApp() {
  try {
    const { NestFactory } = require('@nestjs/core');
    const { AppModule } = require('./dist/app.module');
    
    console.log('Creating NestJS application...');
    const app = await NestFactory.create(AppModule, {
      logger: ['error', 'warn', 'log', 'debug'],
    });
    
    console.log('✅ NestJS application created successfully');
    
    // Test basic configuration
    app.enableCors({
      origin: ['http://localhost:3000', 'http://localhost:3001'],
      credentials: true,
    });
    
    app.setGlobalPrefix('api/v1');
    console.log('✅ Basic configuration applied');
    
    // Try to start the server
    const port = process.env.API_PORT || 3001;
    await app.listen(port, '0.0.0.0');
    
    console.log(`🎉 SUCCESS! API is running on: http://localhost:${port}`);
    console.log(`📚 Swagger docs: http://localhost:${port}/api/docs`);
    console.log(`🔐 Test login: curl -X POST http://localhost:${port}/api/v1/auth/login -H "Content-Type: application/json" -d '{"email":"<EMAIL>","password":"demo123456"}'`);
    
  } catch (error) {
    console.error('❌ NestJS application test failed:', error.message);
    console.error('Stack trace:', error.stack);
    process.exit(1);
  }
}

// Run all tests
async function runDiagnostics() {
  await testDatabase();
  await testNestApp();
}

runDiagnostics();
