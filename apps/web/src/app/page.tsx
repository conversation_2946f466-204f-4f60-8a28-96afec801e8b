'use client';

import { PropertyCard } from '@/components/ui/property-card';
import { Calculator, MapPin, Search, Shield, TrendingUp, Users } from 'lucide-react';
import Link from 'next/link';

export default function HomePage() {

  // Sample property data
  const sampleProperties = [
    {
      id: '1',
      address: '123 Brunswick Street',
      suburb: 'New Farm',
      state: 'QLD',
      price: 1250000,
      bedrooms: 3,
      bathrooms: 2,
      parking: 1,
      propertyType: 'Apartment',
      investmentScore: 85,
      developmentScore: 78,
      growthPotential: 12.5,
      isTracked: false
    },
    {
      id: '2',
      address: '456 Gold Coast Highway',
      suburb: 'Surfers Paradise',
      state: 'QLD',
      price: 875000,
      bedrooms: 2,
      bathrooms: 2,
      parking: 1,
      propertyType: 'Apartment',
      investmentScore: 92,
      developmentScore: 65,
      growthPotential: 18.2,
      isTracked: false
    },
    {
      id: '3',
      address: '789 River Road',
      suburb: 'New Farm',
      state: 'QLD',
      price: 650000,
      bedrooms: 4,
      bathrooms: 3,
      parking: 2,
      propertyType: 'House',
      investmentScore: 73,
      developmentScore: 88,
      growthPotential: 9.8,
      isTracked: false
    }
  ];

  return (
    <div className="bg-gradient-to-br from-blue-50 via-white to-indigo-50 min-h-screen">

      {/* Hero Section */}
      <section className="relative py-24 px-4 sm:px-6 lg:px-8 overflow-hidden">
        {/* Background Elements */}
        <div className="absolute inset-0 bg-gradient-to-br from-primary-50/50 via-transparent to-indigo-50/50"></div>
        <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-full h-full max-w-6xl">
          <div className="absolute top-20 left-10 w-72 h-72 bg-primary-100/30 rounded-full blur-3xl"></div>
          <div className="absolute top-40 right-10 w-96 h-96 bg-indigo-100/30 rounded-full blur-3xl"></div>
        </div>

        <div className="relative max-w-7xl mx-auto text-center">
          <div className="mb-8">
            <div className="inline-flex items-center px-4 py-2 bg-primary-50 border border-primary-200 rounded-full text-primary-700 text-sm font-medium mb-6">
              <span className="w-2 h-2 bg-primary-500 rounded-full mr-2 animate-pulse"></span>
              AI-Powered Property Intelligence Platform
            </div>
          </div>

          <h1 className="text-5xl md:text-7xl font-bold text-gray-900 mb-8 leading-tight">
            Real-Time Property
            <span className="block bg-gradient-to-r from-primary-600 to-indigo-600 bg-clip-text text-transparent">
              Valuations
            </span>
          </h1>

          <p className="text-xl md:text-2xl text-gray-600 mb-12 max-w-4xl mx-auto leading-relaxed">
            Get instant, accurate property valuations powered by our proprietary AI Intelligence Algorithm.
            Real-time market insights and development potential analysis from 18+ premium data sources.
          </p>
          
          {/* Call to Action */}
          <div className="max-w-4xl mx-auto mb-12">
            <div className="text-center">
              <div className="bg-white/80 backdrop-blur-sm rounded-3xl shadow-2xl border border-gray-200/50 p-8 md:p-12">
                <div className="mb-8">
                  <div className="inline-flex items-center px-4 py-2 bg-green-100 border border-green-200 rounded-full text-green-700 text-sm font-medium mb-4">
                    <span className="w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse"></span>
                    Live AI Valuations Available Now
                  </div>
                  <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                    See Your Property's Real Value
                  </h2>
                  <p className="text-lg text-gray-600 mb-8 max-w-2xl mx-auto">
                    Join thousands of property professionals getting instant, accurate valuations.
                    Our AI algorithm analyzes 18+ data sources in real-time to deliver precise market insights.
                  </p>
                </div>

                <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
                  <Link
                    href="/auth/register"
                    className="group relative overflow-hidden bg-gradient-to-r from-primary-600 to-indigo-600 hover:from-primary-700 hover:to-indigo-700 text-white font-bold px-10 py-4 rounded-2xl transition-all duration-300 shadow-2xl hover:shadow-primary-500/25 transform hover:-translate-y-1"
                  >
                    <div className="absolute inset-0 bg-gradient-to-r from-primary-700 to-indigo-700 opacity-0 group-hover:opacity-100 transition-opacity"></div>
                    <span className="relative text-lg">Start Free Trial</span>
                  </Link>
                  <button
                    onClick={() => document.getElementById('demo-section')?.scrollIntoView({ behavior: 'smooth' })}
                    className="group bg-white hover:bg-gray-50 text-gray-700 hover:text-primary-600 font-bold px-10 py-4 rounded-2xl border-2 border-gray-200 hover:border-primary-300 transition-all duration-300 transform hover:-translate-y-1"
                  >
                    <span className="text-lg">See Demo</span>
                  </button>
                </div>

                <div className="text-sm text-gray-500">
                  ✓ No credit card required • ✓ 14-day free trial • ✓ Cancel anytime
                </div>
              </div>
            </div>
          </div>



          {/* AI Valuation Stats */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto">
            <div className="text-center group">
              <div className="bg-white/60 backdrop-blur-sm rounded-2xl p-8 border border-gray-200/50 hover:border-primary-200 transition-all duration-300 hover:shadow-xl">
                <div className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent mb-2">
                  98.7%
                </div>
                <div className="text-gray-600 font-medium">AI Accuracy Rate</div>
                <div className="text-sm text-gray-500 mt-1">Validated against actual sales</div>
              </div>
            </div>
            <div className="text-center group">
              <div className="bg-white/60 backdrop-blur-sm rounded-2xl p-8 border border-gray-200/50 hover:border-primary-200 transition-all duration-300 hover:shadow-xl">
                <div className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent mb-2">
                  18+
                </div>
                <div className="text-gray-600 font-medium">Data Sources</div>
                <div className="text-sm text-gray-500 mt-1">Real-time market intelligence</div>
              </div>
            </div>
            <div className="text-center group">
              <div className="bg-white/60 backdrop-blur-sm rounded-2xl p-8 border border-gray-200/50 hover:border-primary-200 transition-all duration-300 hover:shadow-xl">
                <div className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent mb-2">
                  &lt;3s
                </div>
                <div className="text-gray-600 font-medium">Valuation Speed</div>
                <div className="text-sm text-gray-500 mt-1">Instant AI-powered results</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* AI Valuation Demo Section */}
      <section id="demo-section" className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <div className="inline-flex items-center px-4 py-2 bg-blue-100 border border-blue-200 rounded-full text-blue-700 text-sm font-medium mb-6">
              <span className="w-2 h-2 bg-blue-500 rounded-full mr-2 animate-pulse"></span>
              Live Demo - AI Valuations in Action
            </div>
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              See Our AI Intelligence Algorithm
              <span className="block text-primary-600">at Work</span>
            </h2>
            <p className="text-xl text-gray-600 max-w-4xl mx-auto mb-8">
              Watch how our proprietary AI analyzes real properties in seconds, delivering accurate valuations
              and market insights that traditional methods take days to produce.
            </p>
            <div className="bg-yellow-50 border border-yellow-200 rounded-2xl p-6 max-w-2xl mx-auto">
              <p className="text-yellow-800 font-medium">
                🔒 <strong>Demo Only:</strong> Sign up to access live valuations for any Australian property
              </p>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
            {sampleProperties.map((property) => (
              <div key={property.id} className="relative">
                <PropertyCard
                  property={property}
                  showActions={false}
                />
                <div className="absolute inset-0 bg-white/80 backdrop-blur-sm rounded-2xl flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity duration-300">
                  <div className="text-center p-6">
                    <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <Search className="w-8 h-8 text-primary-600" />
                    </div>
                    <p className="text-gray-700 font-medium mb-2">Full Analysis Available</p>
                    <p className="text-sm text-gray-600">Sign up to unlock detailed insights</p>
                  </div>
                </div>
              </div>
            ))}
          </div>

          <div className="text-center">
            <div className="bg-white rounded-3xl shadow-2xl p-8 max-w-2xl mx-auto">
              <h3 className="text-2xl font-bold text-gray-900 mb-4">
                Ready to Get Real Valuations?
              </h3>
              <p className="text-gray-600 mb-6">
                Join thousands of property professionals getting instant, accurate valuations for any Australian property.
              </p>
              <Link
                href="/auth/register"
                className="inline-block bg-gradient-to-r from-primary-600 to-indigo-600 hover:from-primary-700 hover:to-indigo-700 text-white font-bold px-10 py-4 rounded-2xl transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1"
              >
                Start Free Trial - No Credit Card Required
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* AI Features Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Powered by Advanced AI
              <span className="block text-primary-600">Intelligence Algorithm</span>
            </h2>
            <p className="text-xl text-gray-600 max-w-4xl mx-auto">
              Our proprietary AI system processes millions of data points in real-time to deliver
              the most accurate property valuations and market insights available in Australia.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="text-center p-8 bg-gradient-to-br from-green-50 to-emerald-50 rounded-2xl border border-green-200">
              <div className="w-16 h-16 bg-green-100 rounded-2xl flex items-center justify-center mx-auto mb-6">
                <Calculator className="w-8 h-8 text-green-600" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-3">Instant AI Valuations</h3>
              <p className="text-gray-600 leading-relaxed">
                Get precise property valuations in under 3 seconds using our proprietary AI algorithm
                trained on millions of Australian property transactions.
              </p>
            </div>

            <div className="text-center p-8 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-2xl border border-blue-200">
              <div className="w-16 h-16 bg-blue-100 rounded-2xl flex items-center justify-center mx-auto mb-6">
                <TrendingUp className="w-8 h-8 text-blue-600" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-3">Real-Time Market Data</h3>
              <p className="text-gray-600 leading-relaxed">
                Access live market insights from 18+ premium data sources including sales, listings,
                demographics, and economic indicators updated continuously.
              </p>
            </div>

            <div className="text-center p-8 bg-gradient-to-br from-purple-50 to-pink-50 rounded-2xl border border-purple-200">
              <div className="w-16 h-16 bg-purple-100 rounded-2xl flex items-center justify-center mx-auto mb-6">
                <MapPin className="w-8 h-8 text-purple-600" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-3">Development Potential</h3>
              <p className="text-gray-600 leading-relaxed">
                Discover hidden development opportunities with AI-powered analysis of zoning,
                planning applications, and infrastructure developments.
              </p>
            </div>

            <div className="text-center p-8 bg-gradient-to-br from-orange-50 to-red-50 rounded-2xl border border-orange-200">
              <div className="w-16 h-16 bg-orange-100 rounded-2xl flex items-center justify-center mx-auto mb-6">
                <Shield className="w-8 h-8 text-orange-600" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-3">98.7% Accuracy</h3>
              <p className="text-gray-600 leading-relaxed">
                Industry-leading accuracy validated against actual sales data. Our AI continuously
                learns and improves from every transaction in the market.
              </p>
            </div>

            <div className="text-center p-8 bg-gradient-to-br from-teal-50 to-cyan-50 rounded-2xl border border-teal-200">
              <div className="w-16 h-16 bg-teal-100 rounded-2xl flex items-center justify-center mx-auto mb-6">
                <Users className="w-8 h-8 text-teal-600" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-3">Professional Grade</h3>
              <p className="text-gray-600 leading-relaxed">
                Trusted by property professionals, investors, and developers across Australia
                for critical investment and development decisions.
              </p>
            </div>

            <div className="text-center p-8 bg-gradient-to-br from-gray-50 to-slate-50 rounded-2xl border border-gray-200">
              <div className="w-16 h-16 bg-gray-100 rounded-2xl flex items-center justify-center mx-auto mb-6">
                <Search className="w-8 h-8 text-gray-600" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-3">Any Property, Anywhere</h3>
              <p className="text-gray-600 leading-relaxed">
                Search and analyze any residential or commercial property across Australia
                with comprehensive coverage of all major cities and regions.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="relative py-24 bg-gradient-to-br from-primary-600 via-primary-700 to-indigo-700 overflow-hidden">
        {/* Background Elements */}
        <div className="absolute inset-0">
          <div className="absolute top-0 left-1/4 w-96 h-96 bg-white/10 rounded-full blur-3xl"></div>
          <div className="absolute bottom-0 right-1/4 w-96 h-96 bg-indigo-400/20 rounded-full blur-3xl"></div>
        </div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="mb-8">
            <div className="inline-flex items-center px-4 py-2 bg-white/20 backdrop-blur-sm border border-white/30 rounded-full text-white text-sm font-medium mb-6">
              <span className="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse"></span>
              Join 10,000+ Property Professionals
            </div>
          </div>

          <h2 className="text-4xl md:text-6xl font-bold text-white mb-6 leading-tight">
            Get Instant AI Property
            <span className="block">Valuations Today</span>
          </h2>

          <p className="text-xl md:text-2xl text-primary-100 mb-12 max-w-3xl mx-auto leading-relaxed">
            Join the future of property valuation. Get accurate, real-time valuations for any Australian
            property in under 3 seconds with our advanced AI Intelligence Algorithm.
          </p>

          <div className="flex flex-col sm:flex-row gap-6 justify-center">
            <Link
              href="/auth/register"
              className="group relative overflow-hidden bg-white text-primary-600 hover:text-primary-700 font-bold px-12 py-5 rounded-2xl transition-all duration-300 shadow-2xl hover:shadow-white/25 transform hover:-translate-y-1"
            >
              <div className="absolute inset-0 bg-gradient-to-r from-white to-gray-50 opacity-0 group-hover:opacity-100 transition-opacity"></div>
              <span className="relative text-lg">Get Instant Valuations - Free Trial</span>
            </Link>
            <button
              onClick={() => document.getElementById('demo-section')?.scrollIntoView({ behavior: 'smooth' })}
              className="group bg-white/10 backdrop-blur-sm hover:bg-white/20 text-white font-bold px-12 py-5 rounded-2xl border-2 border-white/30 hover:border-white/50 transition-all duration-300 transform hover:-translate-y-1"
            >
              <span className="text-lg">See AI Demo</span>
            </button>
          </div>

          <div className="mt-12 text-primary-200 text-sm">
            ✓ No credit card required • ✓ 14-day free trial • ✓ 98.7% accuracy guarantee
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-12">
            <div className="md:col-span-2">
              <div className="flex items-center space-x-3 mb-6">
                <div className="w-10 h-10 bg-gradient-to-br from-primary-500 to-primary-700 rounded-xl flex items-center justify-center">
                  <span className="text-white font-bold text-lg">R</span>
                </div>
                <div className="text-3xl font-bold bg-gradient-to-r from-primary-400 to-indigo-400 bg-clip-text text-transparent">
                  Revalu
                </div>
              </div>
              <p className="text-gray-400 text-lg leading-relaxed max-w-md">
                Australia's most accurate AI-powered property valuation platform. Get instant,
                real-time valuations with 98.7% accuracy for any property across Australia.
              </p>
              <div className="mt-6 flex items-center space-x-2 text-sm text-gray-500">
                <span className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></span>
                <span>Live data from 18+ sources</span>
              </div>
            </div>

            <div>
              <h3 className="text-lg font-bold mb-6 text-white">Platform</h3>
              <ul className="space-y-3">
                <li>
                  <Link href="/properties" className="text-gray-400 hover:text-primary-400 transition-colors flex items-center group">
                    <span className="w-1.5 h-1.5 bg-primary-500 rounded-full mr-3 opacity-0 group-hover:opacity-100 transition-opacity"></span>
                    Properties
                  </Link>
                </li>
                <li>
                  <Link href="/development" className="text-gray-400 hover:text-primary-400 transition-colors flex items-center group">
                    <span className="w-1.5 h-1.5 bg-primary-500 rounded-full mr-3 opacity-0 group-hover:opacity-100 transition-opacity"></span>
                    Development Analysis
                  </Link>
                </li>
                <li>
                  <Link href="/market" className="text-gray-400 hover:text-primary-400 transition-colors flex items-center group">
                    <span className="w-1.5 h-1.5 bg-primary-500 rounded-full mr-3 opacity-0 group-hover:opacity-100 transition-opacity"></span>
                    Market Intelligence
                  </Link>
                </li>
                <li>
                  <Link href="/valuations" className="text-gray-400 hover:text-primary-400 transition-colors flex items-center group">
                    <span className="w-1.5 h-1.5 bg-primary-500 rounded-full mr-3 opacity-0 group-hover:opacity-100 transition-opacity"></span>
                    Valuations
                  </Link>
                </li>
              </ul>
            </div>

            <div>
              <h3 className="text-lg font-bold mb-6 text-white">Support</h3>
              <ul className="space-y-3">
                <li>
                  <Link href="/help" className="text-gray-400 hover:text-primary-400 transition-colors flex items-center group">
                    <span className="w-1.5 h-1.5 bg-primary-500 rounded-full mr-3 opacity-0 group-hover:opacity-100 transition-opacity"></span>
                    Help Center
                  </Link>
                </li>
                <li>
                  <Link href="/docs" className="text-gray-400 hover:text-primary-400 transition-colors flex items-center group">
                    <span className="w-1.5 h-1.5 bg-primary-500 rounded-full mr-3 opacity-0 group-hover:opacity-100 transition-opacity"></span>
                    Documentation
                  </Link>
                </li>
                <li>
                  <Link href="/contact" className="text-gray-400 hover:text-primary-400 transition-colors flex items-center group">
                    <span className="w-1.5 h-1.5 bg-primary-500 rounded-full mr-3 opacity-0 group-hover:opacity-100 transition-opacity"></span>
                    Contact Us
                  </Link>
                </li>
                <li>
                  <Link href="/privacy" className="text-gray-400 hover:text-primary-400 transition-colors flex items-center group">
                    <span className="w-1.5 h-1.5 bg-primary-500 rounded-full mr-3 opacity-0 group-hover:opacity-100 transition-opacity"></span>
                    Privacy Policy
                  </Link>
                </li>
              </ul>
            </div>
          </div>

          <div className="border-t border-gray-800 pt-8">
            <div className="flex flex-col md:flex-row justify-between items-center">
              <p className="text-gray-400 text-sm">
                &copy; 2024 Revalu. All rights reserved. Built with ❤️ for property professionals.
              </p>
              <div className="mt-4 md:mt-0 text-sm text-gray-500">
                Made in Australia 🇦🇺
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
